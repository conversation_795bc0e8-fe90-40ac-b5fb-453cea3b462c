#!/usr/bin/env python3
"""
StealthSeleniumBase Usage Examples
=================================

This file demonstrates how to use the StealthSeleniumBase package
across different projects and scenarios.

Examples include:
1. Basic stealth browser creation
2. Integration with existing proxy extensions
3. Timezone matching with proxy
4. Behavioral simulation
5. Compatibility with existing codebase patterns
"""

import os
import logging
from stealth_seleniumbase import (
    StealthSeleniumBase,
    StealthBehavioralSimulator,
    create_stealth_browser_with_extensions,
    create_enhanced_stealth_driver,
    apply_stealth_to_existing_driver,
    StealthBrowserFactory
)

# Setup logging for examples
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def example_1_basic_stealth_browser():
    """Example 1: Create a basic stealth browser with UC Mode."""
    print("\n=== Example 1: Basic Stealth Browser ===")
    
    # Create stealth browser instance
    stealth_browser = StealthSeleniumBase(
        profile_path="./test_profile",
        proxy_config={
            "host": "proxy.example.com",
            "port": 8080,
            "username": "user",
            "password": "pass"
        }
    )
    
    try:
        # Create browser with stealth
        browser = stealth_browser.create_stealth_browser(
            headless=False,
            use_sb_context=True
        )
        
        # Apply additional stealth techniques
        stealth_browser.apply_additional_stealth()
        
        # Navigate with UC Mode stealth
        stealth_browser.uc_open_with_reconnect("https://example.com")
        
        # Check if timezone was detected and applied
        timezone_info = stealth_browser.get_timezone_info()
        if timezone_info:
            print(f"Proxy timezone detected: {timezone_info['timezone']}")
        
        print("Basic stealth browser created successfully!")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        # Clean up
        stealth_browser.cleanup_browser()


def example_2_extension_based_proxy():
    """Example 2: Use stealth browser with Chrome extensions (existing pattern)."""
    print("\n=== Example 2: Extension-Based Proxy ===")
    
    # Paths for existing extension setup
    profile_path = "./Profiles/test_profile"
    extensions_path = "./extensions/shared_proxy_extension,./extensions/disable_csp,./extensions/webrtc"
    
    proxy_config = {
        "host": "**************",
        "port": 12323,
        "username": "14a2c9baf5c69",
        "password": "d98cb3bf2a"
    }
    
    try:
        # Create stealth browser with extensions (compatible with existing patterns)
        stealth_browser = create_stealth_browser_with_extensions(
            profile_path=profile_path,
            extensions_path=extensions_path,
            proxy_config=proxy_config,
            headless=False
        )
        
        # Navigate with stealth
        stealth_browser.uc_open_with_reconnect("https://whatismyipaddress.com")
        
        # Activate CDP mode for maximum stealth
        stealth_browser.activate_cdp_mode()
        
        print("Extension-based stealth browser created successfully!")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        stealth_browser.cleanup_browser()


def example_3_enhanced_driver_compatibility():
    """Example 3: Integration with existing EnhancedSeleniumBaseDriver patterns."""
    print("\n=== Example 3: Enhanced Driver Compatibility ===")
    
    # Simulate existing profile configuration
    email = "<EMAIL>"
    profile_config = {
        "profile_path": "./Profiles/test_profile",
        "email": email
    }
    
    proxy_config = {
        "host": "gw.dataimpulse.com",
        "port": 823,
        "username": "e98f5489956302bda457__cr.gb",
        "password": "917b45d9ec594d54"
    }
    
    extensions_dir = "./extensions/shared_proxy_extension,./extensions/disable_csp"
    
    try:
        # Create enhanced stealth driver (compatible with existing patterns)
        stealth_browser = create_enhanced_stealth_driver(
            email=email,
            profile_config=profile_config,
            proxy_config=proxy_config,
            extensions_dir=extensions_dir
        )
        
        # Use existing navigation patterns
        stealth_browser.uc_open_with_reconnect("https://accounts.google.com")
        
        print(f"Enhanced stealth driver created for {email}")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        stealth_browser.cleanup_browser()


def example_4_behavioral_simulation():
    """Example 4: Using behavioral simulation for human-like interactions."""
    print("\n=== Example 4: Behavioral Simulation ===")
    
    stealth_browser = StealthSeleniumBase(profile_path="./test_profile")
    
    try:
        browser = stealth_browser.create_stealth_browser()
        
        # Create behavioral simulator
        behavioral_sim = StealthBehavioralSimulator(
            browser=browser,
            seed=12345  # Consistent behavior
        )
        
        # Navigate to a page
        stealth_browser.uc_open_with_reconnect("https://example.com")
        
        # Simulate human-like delays
        behavioral_sim.simulate_human_delay(2.0, 4.0)
        
        # Simulate reading time
        behavioral_sim.simulate_reading_time(text_length=500)
        
        # Get human-like scroll amount
        scroll_amount = behavioral_sim.get_human_scroll_amount()
        print(f"Human-like scroll amount: {scroll_amount}px")
        
        # Get click offset for more natural clicking
        offset_x, offset_y = behavioral_sim.get_human_click_offset()
        print(f"Click offset: ({offset_x}, {offset_y})")
        
        print("Behavioral simulation completed!")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        stealth_browser.cleanup_browser()


def example_5_factory_patterns():
    """Example 5: Using factory patterns for different browser types."""
    print("\n=== Example 5: Factory Patterns ===")
    
    profile_path = "./test_profile"
    extensions_path = "./extensions/shared_proxy_extension"
    proxy_config = {"host": "proxy.example.com", "port": 8080}
    
    try:
        # Create UC Mode browser
        uc_browser = StealthBrowserFactory.create_uc_mode_browser(
            profile_path=profile_path,
            extensions_path=extensions_path,
            proxy_config=proxy_config
        )
        print("UC Mode stealth browser created")
        uc_browser.cleanup_browser()
        
        # Create headless browser
        headless_browser = StealthBrowserFactory.create_headless_stealth_browser(
            profile_path=profile_path,
            proxy_config=proxy_config
        )
        print("Headless stealth browser created")
        headless_browser.cleanup_browser()
        
        # Create CDP Mode browser for maximum stealth
        cdp_browser = StealthBrowserFactory.create_cdp_mode_browser(
            profile_path=profile_path,
            extensions_path=extensions_path,
            proxy_config=proxy_config
        )
        print("CDP Mode stealth browser created")
        cdp_browser.cleanup_browser()
        
    except Exception as e:
        print(f"Error: {e}")


def example_6_retrofit_existing_driver():
    """Example 6: Apply stealth to existing WebDriver instance."""
    print("\n=== Example 6: Retrofit Existing Driver ===")
    
    try:
        from seleniumbase import Driver as SBDriver
        
        # Create a regular SeleniumBase driver
        driver = SBDriver(uc=True, headless=False)
        
        # Apply stealth techniques to existing driver
        proxy_config = {"host": "proxy.example.com", "port": 8080}
        apply_stealth_to_existing_driver(
            driver=driver,
            proxy_config=proxy_config,
            logger=logger
        )
        
        print("Stealth techniques applied to existing driver!")
        
        # Use the driver normally
        driver.get("https://example.com")
        
        # Clean up
        driver.quit()
        
    except Exception as e:
        print(f"Error: {e}")


def example_7_context_manager():
    """Example 7: Using stealth browser as context manager."""
    print("\n=== Example 7: Context Manager Usage ===")
    
    proxy_config = {
        "host": "proxy.example.com",
        "port": 8080,
        "username": "user",
        "password": "pass"
    }
    
    # Use as context manager for automatic cleanup
    with StealthSeleniumBase(
        profile_path="./test_profile",
        proxy_config=proxy_config
    ) as stealth_browser:
        
        # Create browser
        browser = stealth_browser.create_stealth_browser()
        stealth_browser.apply_additional_stealth()
        
        # Navigate
        stealth_browser.uc_open_with_reconnect("https://example.com")
        
        # Check timezone info
        timezone_info = stealth_browser.get_timezone_info()
        if timezone_info:
            print(f"Timezone: {timezone_info['timezone']}")
        
        print("Context manager usage completed!")
        # Automatic cleanup when exiting context


if __name__ == "__main__":
    print("StealthSeleniumBase Usage Examples")
    print("=" * 50)
    
    # Run examples (comment out as needed for testing)
    try:
        example_1_basic_stealth_browser()
        example_2_extension_based_proxy()
        example_3_enhanced_driver_compatibility()
        example_4_behavioral_simulation()
        example_5_factory_patterns()
        example_6_retrofit_existing_driver()
        example_7_context_manager()
        
    except KeyboardInterrupt:
        print("\nExamples interrupted by user")
    except Exception as e:
        print(f"\nUnexpected error: {e}")
    
    print("\nAll examples completed!")
